#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量生成93段汇总图演示脚本
处理指定文件夹中的前几个音频文件作为演示
"""

import os
import sys
import time
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adaptive_fundamental_removal import main as thd_main
from create_summary_visualization import parse_results_file
import numpy as np
import matplotlib.pyplot as plt

# 设置matplotlib字体
import matplotlib.font_manager as fm
available_fonts = [f.name for f in fm.fontManager.ttflist]
chinese_fonts = []
common_chinese = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
for font in common_chinese:
    if font in available_fonts:
        chinese_fonts.append(font)

plt.rcParams.update({
    'font.sans-serif': chinese_fonts + ['Deja<PERSON>u Sans', 'Arial', 'sans-serif'],
    'axes.unicode_minus': False,
    'font.family': 'sans-serif',
    'font.size': 10,
})

def create_summary_visualization_with_filename(results, output_dir, filename):
    """创建93段结果汇总可视化 - 三张图竖向排列，标题包含文件名"""

    # 提取数据
    segments = [r['segment_idx'] for r in results]
    method1_thd_n = [r['thd_n_method1'] for r in results]
    method2_thd_n = [r['thd_n_method2'] for r in results]
    scaling_factors = [r['scaling_factor'] for r in results]

    # 创建三张图竖向排列
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 18))

    # 1. 方法一THD+N分布
    ax1.bar(segments, method1_thd_n, alpha=0.8, color='red',
           label='Method 1 (Time Domain Power Subtraction)')

    ax1.set_xlabel('Segment Number', fontsize=12)
    ax1.set_ylabel('THD+N (%)', fontsize=12)
    # 检查是否有正值，决定是否使用对数坐标
    if any(val > 0 for val in method1_thd_n):
        ax1.set_yscale('log')
    ax1.set_title(f'Method 1: THD+N Distribution - {filename}', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.legend()
    ax1.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    method1_avg = np.mean(method1_thd_n)
    method1_min = np.min(method1_thd_n)
    method1_max = np.max(method1_thd_n)
    ax1.text(0.02, 0.98, f'Avg: {method1_avg:.3f}%\nMin: {method1_min:.3f}%\nMax: {method1_max:.3f}%',
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))

    # 2. 方法二THD+N分布
    ax2.bar(segments, method2_thd_n, alpha=0.8, color='blue',
           label='Method 2 (Fundamental Bandwidth Exclusion, ≥100Hz)')

    ax2.set_xlabel('Segment Number', fontsize=12)
    ax2.set_ylabel('THD+N (%)', fontsize=12)
    ax2.set_yscale('log')
    ax2.set_title(f'Method 2: THD+N Distribution - {filename}', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.legend()
    ax2.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    method2_avg = np.mean(method2_thd_n)
    method2_min = np.min(method2_thd_n)
    method2_max = np.max(method2_thd_n)
    ax2.text(0.02, 0.98, f'Avg: {method2_avg:.3f}%\nMin: {method2_min:.3f}%\nMax: {method2_max:.3f}%',
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 3. 参考音频缩放系数分布
    ax3.bar(segments, scaling_factors, alpha=0.8, color='green',
           label='Reference Audio Scaling Factor')

    ax3.set_xlabel('Segment Number', fontsize=12)
    ax3.set_ylabel('Scaling Factor', fontsize=12)
    ax3.set_title(f'Reference Audio Scaling Factor Distribution - {filename}', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3, axis='y')
    ax3.legend()
    ax3.set_xlim(0, len(segments) + 1)

    # 添加统计信息
    scaling_avg = np.mean(scaling_factors)
    scaling_min = np.min(scaling_factors)
    scaling_max = np.max(scaling_factors)
    ax3.text(0.02, 0.98, f'Avg: {scaling_avg:.6f}\nMin: {scaling_min:.6f}\nMax: {scaling_max:.6f}',
             transform=ax3.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()

    # 保存图片，降低DPI以减少内存使用
    output_file = os.path.join(output_dir, "93段THD+N分析汇总.png")
    plt.savefig(output_file, dpi=150, bbox_inches='tight')  # 降低DPI从300到150
    plt.close()

    # 强制垃圾回收
    import gc
    gc.collect()

    print(f"📊 汇总图已保存: {output_file}")

def find_audio_files(folder_path, limit=None):
    """查找文件夹中的音频文件"""
    audio_extensions = ['*.wav', '*.mp3', '*.flac', '*.aiff', '*.m4a']
    audio_files = []
    
    folder_path = Path(folder_path)
    if not folder_path.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in audio_extensions:
        files = list(folder_path.glob(f"**/{ext}"))
        audio_files.extend(files)
    
    audio_files = sorted(audio_files)
    
    if limit:
        audio_files = audio_files[:limit]
    
    return audio_files

def process_single_audio(audio_file, batch_output_dir):
    """处理单个音频文件"""
    print(f"\n🎵 处理: {audio_file.name}")
    print(f"📂 来源: {audio_file.parent}")

    try:
        # 1. 运行THD+N分析
        print("🔬 运行THD+N分析...")
        success = thd_main(str(audio_file))

        if not success:
            print("❌ THD+N分析失败")
            return False

        # 2. 查找分析结果
        analysis_dir = f"{audio_file.stem}_THD+N双方法分析"
        summary_file = os.path.join(analysis_dir, "THD+N双方法分析汇总.txt")

        if not os.path.exists(summary_file):
            print(f"❌ 汇总文件不存在: {summary_file}")
            return False

        # 3. 解析汇总文件并生成带文件名的汇总图
        print("📊 生成汇总图...")
        results = parse_results_file(summary_file)

        if not results:
            print("❌ 解析汇总文件失败")
            return False

        # 修改create_summary_visualization函数调用，传入文件名信息
        create_summary_visualization_with_filename(results, analysis_dir, audio_file.name)

        # 4. 按原路径结构创建输出目录
        relative_path = audio_file.parent.relative_to(Path("."))
        output_subdir = os.path.join(batch_output_dir, str(relative_path))
        os.makedirs(output_subdir, exist_ok=True)

        # 5. 复制汇总图到对应的子目录
        summary_image = os.path.join(analysis_dir, "93段THD+N分析汇总.png")
        if os.path.exists(summary_image):
            dest_name = f"{audio_file.stem}_汇总图.png"
            dest_path = os.path.join(output_subdir, dest_name)
            shutil.copy2(summary_image, dest_path)
            print(f"📊 汇总图已保存: {relative_path}/{dest_name}")

            # 清理分析目录（可选）
            # shutil.rmtree(analysis_dir)

            return True
        else:
            print("❌ 汇总图生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 批量生成93段汇总图演示")
    print("=" * 50)
    
    # 定义要处理的文件夹和数量限制
    target_folders = [
        ("test20250717", None),  # 处理2个文件
        ("待定", None),          # 处理1个文件  
        ("test20250722", None)   # 处理2个文件
    ]
    
    # 创建批量输出目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    batch_output_dir = f"批量汇总图演示_{timestamp}"
    os.makedirs(batch_output_dir, exist_ok=True)
    print(f"📁 输出目录: {batch_output_dir}")
    
    # 收集要处理的音频文件
    all_audio_files = []
    for folder, limit in target_folders:
        print(f"\n📂 扫描文件夹: {folder} (限制{limit}个文件)")
        audio_files = find_audio_files(folder, limit)
        
        if audio_files:
            print(f"   选择 {len(audio_files)} 个音频文件")
            for f in audio_files:
                print(f"     - {f.name}")
            all_audio_files.extend(audio_files)
        else:
            print(f"   未找到音频文件")
    
    if not all_audio_files:
        print("❌ 未找到任何音频文件")
        return
    
    print(f"\n📊 总共将处理 {len(all_audio_files)} 个音频文件")
    
    # 处理所有文件
    successful = 0
    failed = 0
    start_time = time.time()
    
    for i, audio_file in enumerate(all_audio_files, 1):
        print(f"\n{'='*60}")
        print(f"[{i}/{len(all_audio_files)}] 处理进度")
        
        try:
            success = process_single_audio(audio_file, batch_output_dir)
            if success:
                successful += 1
                print(f"✅ {audio_file.name} 处理完成")
            else:
                failed += 1
                print(f"❌ {audio_file.name} 处理失败")
        except Exception as e:
            print(f"❌ {audio_file.name} 处理异常: {str(e)}")
            failed += 1
    
    # 创建处理报告
    report_file = os.path.join(batch_output_dir, "处理报告.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("批量汇总图生成演示报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总文件数: {len(all_audio_files)}\n")
        f.write(f"成功处理: {successful}\n")
        f.write(f"处理失败: {failed}\n")
        f.write(f"成功率: {successful/len(all_audio_files)*100:.1f}%\n\n")
        
        f.write("处理的文件列表:\n")
        f.write("-" * 30 + "\n")
        for i, audio_file in enumerate(all_audio_files, 1):
            f.write(f"{i}. {audio_file.parent.name}/{audio_file.name}\n")
    
    # 总结
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print("🎉 批量处理演示完成!")
    print(f"📊 处理统计:")
    print(f"   总文件数: {len(all_audio_files)}")
    print(f"   成功处理: {successful}")
    print(f"   处理失败: {failed}")
    print(f"   成功率: {successful/len(all_audio_files)*100:.1f}%")
    print(f"   总耗时: {total_time:.1f}秒")
    if successful > 0:
        print(f"   平均耗时: {total_time/successful:.1f}秒/文件")
    print(f"📁 输出目录: {batch_output_dir}")
    print(f"📄 处理报告: {report_file}")
    print("=" * 60)

if __name__ == "__main__":
    main()
