#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应幅值缩放 + 频域相减方案
使用参考信号作为标准音频，去除待测音频的主频
"""

import os
import sys
import numpy as np
import librosa
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor
import time

# 添加harmonic_detection_system路径
sys.path.append('harmonic_detection_system')
from chirp import gen_freq_step

# 尝试导入matplotlib
try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    # 设置字体和负号显示
    import matplotlib.font_manager as fm

    # 查找可用的中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = []

    # 检查常见中文字体是否可用
    common_chinese = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong', 'Arial Unicode MS']
    for font in common_chinese:
        if font in available_fonts:
            chinese_fonts.append(font)

    # 如果没有找到中文字体，使用默认字体
    if not chinese_fonts:
        chinese_fonts = ['DejaVu Sans', 'Arial','Microsoft YaHei']

    plt.rcParams.update({
        'font.sans-serif': chinese_fonts + ['DejaVu Sans', 'Arial', 'sans-serif'],
        'axes.unicode_minus': False,  # 解决负号显示问题
        'font.family': 'sans-serif',
        'text.usetex': False,
        'mathtext.default': 'regular',
        'font.size': 10,
    })

    # 清除字体缓存
    try:
        fm._rebuild()
    except AttributeError:
        # 新版本matplotlib可能没有_rebuild方法
        pass
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ matplotlib未安装，将跳过图像生成")


def frequency_domain_subtraction(test_fft, ref_fft, scaling_factor):
    """
    频域相减：从待测信号中减去缩放后的参考信号
    
    Args:
        test_fft: 待测音频的FFT
        ref_fft: 参考信号的FFT  
        scaling_factor: 缩放因子
    
    Returns:
        result_fft: 相减后的FFT
    """
    # 缩放参考信号
    scaled_ref_fft = ref_fft * scaling_factor
    
    # 频域相减
    result_fft = test_fft - scaled_ref_fft
    
    return result_fft

def get_bandwidth_strategy(frequency):
    """
    根据频率确定带宽策略
    100-200Hz: 使用百分比带宽
    200-20000Hz: 使用固定200Hz带宽
    """
    if 100 <= frequency < 200:
        # 低频段使用百分比带宽
        percent = 20.0
        return 'percent', percent
    elif 200 <= frequency:
        # 中高频段使用固定200Hz带宽
        return 'fixed', 200.0

def analyze_segment_adaptive_removal(args):
    """
    分析单个频段，对比待测信号和参考信号的原始频谱
    """
    seg_idx, test_start_time, test_end_time, ref_start_time, ref_end_time, expected_freq, test_audio, ref_audio, sr, output_dir = args
    
    try:
        print(f"  分析频段 {seg_idx:2d}: {expected_freq:6.1f}Hz")
        print(f"    待测: {test_start_time:.3f}s - {test_end_time:.3f}s")
        print(f"    参考: {ref_start_time:.3f}s - {ref_end_time:.3f}s")

        # 分别提取待测音频和参考音频段
        # 待测音频段（使用freq_split_optimized的时间）
        test_start_sample = int(test_start_time * sr)
        test_end_sample = int(test_end_time * sr)

        # 参考音频段（使用gen_freq_step的时间，从0开始）
        ref_start_sample = int(ref_start_time * sr)
        ref_end_sample = int(ref_end_time * sr)

        # 确保索引在有效范围内
        test_start_sample = max(0, min(test_start_sample, len(test_audio)))
        test_end_sample = max(0, min(test_end_sample, len(test_audio)))
        ref_start_sample = max(0, min(ref_start_sample, len(ref_audio)))
        ref_end_sample = max(0, min(ref_end_sample, len(ref_audio)))

        test_total_segment = test_audio[test_start_sample:test_end_sample]
        ref_total_segment = ref_audio[ref_start_sample:ref_end_sample]

        # 检查音频段长度
        if len(test_total_segment) == 0:
            print(f"    ❌ 待测音频段为空: {test_start_sample}-{test_end_sample}")
            return False, None
        if len(ref_total_segment) == 0:
            print(f"    ❌ 参考音频段为空: {ref_start_sample}-{ref_end_sample}")
            return False, None

        print(f"    音频段长度: 待测={len(test_total_segment)}, 参考={len(ref_total_segment)}")

        if len(test_total_segment) == 0:
            print(f"    警告: 段 {seg_idx} 待测音频长度为0")
            return False, None
        if len(ref_total_segment) == 0:
            print(f"    警告: 段 {seg_idx} 参考音频长度为0")
            return False, None

        # 计算最中间100ms的位置
        # 去掉开头和结尾各8%，保留中间84%
        trim_ratio = 0.08  # 开头和结尾各去掉8%

        # 待测音频段处理 - 去掉开头和结尾各8%
        test_total_length = len(test_total_segment)
        test_trim_samples = int(test_total_length * trim_ratio)
        test_start_idx = test_trim_samples
        test_end_idx = test_total_length - test_trim_samples
        test_segment = test_total_segment[test_start_idx:test_end_idx]

        # 参考音频段处理 - 去掉开头和结尾各8%
        ref_total_length = len(ref_total_segment)
        ref_trim_samples = int(ref_total_length * trim_ratio)
        ref_start_idx = ref_trim_samples
        ref_end_idx = ref_total_length - ref_trim_samples
        ref_segment = ref_total_segment[ref_start_idx:ref_end_idx]

        # 暂时不标准化，先看原始频谱
        print(f"    待测段: 长度={len(test_segment)}, 最大值={np.max(np.abs(test_segment)):.6f}")
        print(f"    参考段: 长度={len(ref_segment)}, 最大值={np.max(np.abs(ref_segment)):.6f}")
        
        # FFT分析 - 使用输入信号长度，确保两个信号长度一致
        test_length = len(test_segment)
        ref_length = len(ref_segment)

        # 取两个信号的最小长度，确保长度一致
        min_length = min(test_length, ref_length)

        # 截取到相同长度
        if test_length > min_length:
            # 从中间截取
            start_idx = (test_length - min_length) // 2
            test_segment = test_segment[start_idx:start_idx + min_length]

        if ref_length > min_length:
            # 从中间截取
            start_idx = (ref_length - min_length) // 2
            ref_segment = ref_segment[start_idx:start_idx + min_length]

        # FFT长度使用固定12000
        fft_size = 12000

        # 如果信号长度小于FFT长度，进行零填充
        if min_length < fft_size:
            # 零填充到FFT长度
            test_segment_padded = np.zeros(fft_size)
            ref_segment_padded = np.zeros(fft_size)

            # 将信号放在中间
            start_idx = (fft_size - min_length) // 2
            test_segment_padded[start_idx:start_idx + min_length] = test_segment
            ref_segment_padded[start_idx:start_idx + min_length] = ref_segment

            test_segment = test_segment_padded
            ref_segment = ref_segment_padded
        elif min_length > fft_size:
            # 如果信号长度大于FFT长度，从中间截取
            start_idx = (min_length - fft_size) // 2
            test_segment = test_segment[start_idx:start_idx + fft_size]
            ref_segment = ref_segment[start_idx:start_idx + fft_size]

        window = np.hanning(fft_size)
        test_windowed = test_segment * window
        ref_windowed = ref_segment * window

        test_fft = np.fft.fft(test_windowed)
        ref_fft = np.fft.fft(ref_windowed)

        # 检查FFT结果
        if np.any(np.isnan(test_fft)) or np.any(np.isinf(test_fft)):
            print(f"    ❌ 待测FFT包含NaN或Inf")
            return False, None
        if np.any(np.isnan(ref_fft)) or np.any(np.isinf(ref_fft)):
            print(f"    ❌ 参考FFT包含NaN或Inf")
            return False, None

        freqs_fft = np.fft.fftfreq(fft_size, 1/sr)

        # 只取正频率
        positive_freqs = freqs_fft[:fft_size//2]
        test_fft_pos = test_fft[:fft_size//2]
        ref_fft_pos = ref_fft[:fft_size//2]

        print(f"    FFT长度: {len(test_fft_pos)}, 频率范围: {positive_freqs[0]:.1f}-{positive_freqs[-1]:.1f}Hz")
        
        # 限制显示范围到0~24kHz
        freq_mask = (positive_freqs >= 0) & (positive_freqs <= 24000)
        display_freqs = positive_freqs[freq_mask]
        test_fft_display = test_fft_pos[freq_mask]
        ref_fft_display = ref_fft_pos[freq_mask]
        
        # 分别找待测信号和参考信号的主频峰值
        search_bandwidth = 10.0  # 扩大搜索范围到±10Hz
        search_mask = (display_freqs >= expected_freq - search_bandwidth) & \
                     (display_freqs <= expected_freq + search_bandwidth)

        # 找待测信号的峰值
        if np.any(search_mask):
            test_search_powers = np.abs(test_fft_display[search_mask]) ** 2
            if len(test_search_powers) == 0:
                print(f"    ❌ 待测信号搜索范围内无数据")
                return False, None
            test_max_power_idx = np.argmax(test_search_powers)
            test_search_indices = np.where(search_mask)[0]
            test_peak_idx = test_search_indices[test_max_power_idx]
            test_fundamental_freq = display_freqs[test_peak_idx]
            test_peak_amp = np.abs(test_fft_display[test_peak_idx])
        else:
            print(f"    ❌ 待测信号搜索范围{expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz无数据")
            return False, None

        # 找参考信号的峰值
        if np.any(search_mask):
            ref_search_powers = np.abs(ref_fft_display[search_mask]) ** 2
            if len(ref_search_powers) == 0:
                print(f"    ❌ 参考信号搜索范围内无数据")
                return False, None
            ref_max_power_idx = np.argmax(ref_search_powers)
            ref_search_indices = np.where(search_mask)[0]
            ref_peak_idx = ref_search_indices[ref_max_power_idx]
            ref_fundamental_freq = display_freqs[ref_peak_idx]
            ref_peak_amp = np.abs(ref_fft_display[ref_peak_idx])
        else:
            print(f"    ❌ 参考信号搜索范围{expected_freq-search_bandwidth:.1f}-{expected_freq+search_bandwidth:.1f}Hz无数据")
            return False, None

        # 计算缩放因子，将参考信号缩放到待测信号的幅值
        if ref_peak_amp > 0:
            scaling_factor = test_peak_amp / ref_peak_amp
        else:
            print(f"    ❌ 参考信号峰值为0，无法计算缩放因子")
            return False, None

        # 检查缩放因子合理性
        if scaling_factor <= 0 or np.isnan(scaling_factor) or np.isinf(scaling_factor):
            print(f"    ❌ 缩放因子异常: {scaling_factor}")
            return False, None

        print(f"    待测峰值: {test_fundamental_freq:.1f}Hz, 幅值={test_peak_amp:.2e}")
        print(f"    参考峰值: {ref_fundamental_freq:.1f}Hz, 幅值={ref_peak_amp:.2e}")
        print(f"    频率偏差: {abs(test_fundamental_freq - ref_fundamental_freq):.1f}Hz")
        print(f"    幅值对齐: 缩放因子={scaling_factor:.6f}")

        # 对参考信号在时域进行缩放
        ref_windowed_scaled = ref_windowed * scaling_factor

        # 重新计算缩放后参考信号的FFT
        ref_fft_scaled = np.fft.fft(ref_windowed_scaled)
        ref_fft_scaled_pos = ref_fft_scaled[:fft_size//2]

        # 更新显示用的缩放后参考信号FFT
        ref_fft_scaled_display = ref_fft_scaled_pos[freq_mask]

        # 方法1：时域功率相减法计算THD+N
        ref_segment_scaled = ref_segment * scaling_factor
        ref_power = np.mean(ref_segment_scaled**2)
        test_power = np.mean(test_segment**2)
        noise_power = test_power - ref_power
        thd_n_method1 = (noise_power / test_power) * 100 if test_power > 0 else 0

        # 计算功率谱用于方法2（从100Hz开始）
        test_power_spectrum = np.abs(test_fft_pos)**2 / fft_size**2

        # 找到100Hz对应的bin
        freq_100hz_bin = int(100 * fft_size / sr)

        # 从100Hz开始计算总功率
        total_power_freq = np.sum(test_power_spectrum[freq_100hz_bin:])

        # 方法2：混合带宽策略排除法计算THD+N
        # 根据频率确定带宽策略
        bandwidth_type, bandwidth_value = get_bandwidth_strategy(test_fundamental_freq)

        # 计算主频带宽范围
        if bandwidth_type == 'percent':
            # 百分比带宽
            bandwidth_hz = test_fundamental_freq * bandwidth_value / 100.0
            fundamental_bandwidth_percent = bandwidth_value
        else:
            # 固定带宽
            bandwidth_hz = bandwidth_value
            fundamental_bandwidth_percent = (bandwidth_value / test_fundamental_freq) * 100.0

        fundamental_freq_start = test_fundamental_freq - bandwidth_hz / 2
        fundamental_freq_end = test_fundamental_freq + bandwidth_hz / 2

        # 转换为bin索引
        freq_resolution = sr / fft_size
        fundamental_start_bin = int(fundamental_freq_start / freq_resolution)
        fundamental_end_bin = int(fundamental_freq_end / freq_resolution)

        # 确保索引在有效范围内
        fundamental_start_bin = max(0, fundamental_start_bin)
        fundamental_end_bin = min(len(test_power_spectrum), fundamental_end_bin)

        # 排除主频带宽（确保在100Hz以上范围内）
        # 调整主频bin范围，确保不超出100Hz以上的范围
        fundamental_start_bin_adj = max(fundamental_start_bin, freq_100hz_bin)
        fundamental_end_bin_adj = min(fundamental_end_bin, len(test_power_spectrum))

        fundamental_power = np.sum(test_power_spectrum[fundamental_start_bin_adj:fundamental_end_bin_adj])

        # THD+N = (总功率 - 主频功率) / 总功率
        thd_power_method2 = total_power_freq - fundamental_power
        thd_n_method2 = (thd_power_method2 / total_power_freq) * 100 if total_power_freq > 0 else 0

        print(f"    方法1 THD+N(时域功率相减): {thd_n_method1:.3f}%")
        if bandwidth_type == 'percent':
            print(f"    方法2 THD+N(百分比带宽排除,≥100Hz): {thd_n_method2:.3f}% (排除{bandwidth_value:.1f}%带宽)")
        else:
            print(f"    方法2 THD+N(固定带宽排除,≥100Hz): {thd_n_method2:.3f}% (排除{bandwidth_value:.0f}Hz带宽)")
        print(f"    主频{test_fundamental_freq:.1f}Hz -> 策略:{bandwidth_type}, 实际带宽:{bandwidth_hz:.1f}Hz, 范围: {fundamental_freq_start:.1f}-{fundamental_freq_end:.1f}Hz")
        print(f"    方法2功率计算范围: ≥100Hz (bin {freq_100hz_bin}), 总功率: {total_power_freq:.3e}")

        # 计算幅度谱用于可视化（display版本，归一化）
        test_amp_vis = np.abs(test_fft_display) / fft_size
        ref_amp_vis = np.abs(ref_fft_display) / fft_size
        ref_amp_scaled_vis = np.abs(ref_fft_scaled_display) / fft_size

        
        # 可视化 - 显示原始频谱和幅值对齐后的对比
        if MATPLOTLIB_AVAILABLE:
            fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(16, 24))

            # 4个子图竖向排列：
            # ax1: 原始幅度谱对比
            # ax2: 时域信号对比
            # ax3: 对齐后幅度谱对比
            # ax4: 主频附近放大

            # 设置频率显示范围
            min_freq_display = max(1, np.min(display_freqs[display_freqs > 0]))
            max_freq_display = min(24000, np.max(display_freqs))

            # 上图：原始幅度谱对比（全频段）
            ax1.semilogy(display_freqs, test_amp_vis, 'b-', linewidth=1.0, alpha=0.8, label='Test Signal')
            ax1.semilogy(display_freqs, ref_amp_vis, 'r-', linewidth=1.0, alpha=0.8, label='Reference Signal (Original)')

            # 标记不同的主频峰值
            test_peak_idx = np.argmin(np.abs(display_freqs - test_fundamental_freq))
            ref_peak_idx = np.argmin(np.abs(display_freqs - ref_fundamental_freq))
            ax1.plot(test_fundamental_freq, test_amp_vis[test_peak_idx], 'o', color='blue', markersize=8,
                    markeredgecolor='darkblue', markeredgewidth=1.5, label=f'待测峰值 {test_fundamental_freq:.1f}Hz')
            ax1.plot(ref_fundamental_freq, ref_amp_vis[ref_peak_idx], 's', color='red', markersize=8,
                    markeredgecolor='darkred', markeredgewidth=1.5, label=f'参考峰值 {ref_fundamental_freq:.1f}Hz')

            ax1.set_xlim(min_freq_display, max_freq_display)
            ax1.set_xlabel('Frequency (Hz)')
            ax1.set_ylabel('Amplitude Spectrum')
            ax1.set_title(f'Segment {seg_idx:02d}: {expected_freq:.1f}Hz - Original Amplitude Spectrum',
                         fontsize=14, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 右上图：方法二功率谱分解可视化
            # 显示完整的功率谱和排除的主频部分
            ax2.semilogy(display_freqs, test_power_spectrum[freq_mask], 'b-', linewidth=1.0, alpha=0.8, label='Total Power Spectrum')

            # 创建排除主频后的功率谱
            test_power_spectrum_method2 = test_power_spectrum.copy()
            test_power_spectrum_method2[fundamental_start_bin:fundamental_end_bin] = 0

            ax2.semilogy(display_freqs, test_power_spectrum_method2[freq_mask], 'r-', linewidth=1.0, alpha=0.8,
                        label='Power After Fundamental Exclusion')

            # 显示被排除的主频功率
            excluded_power = test_power_spectrum.copy()
            excluded_power[:fundamental_start_bin] = 0
            excluded_power[fundamental_end_bin:] = 0
            ax2.semilogy(display_freqs, excluded_power[freq_mask], 'y-', linewidth=2.0, alpha=0.8,
                        label=f'Excluded Fundamental Power ({fundamental_bandwidth_percent:.1f}% bandwidth)')

            # 显示置零区域
            ax2.axvspan(fundamental_freq_start, fundamental_freq_end, alpha=0.2, color='yellow')

            # 显示100Hz分界线
            ax2.axvline(100, color='red', linestyle='--', alpha=0.8, linewidth=2, label='100Hz Power Calculation Start')

            ax2.set_xlim(min_freq_display, max_freq_display)
            ax2.set_xlabel('Frequency (Hz)')
            ax2.set_ylabel('Power Spectrum')
            ax2.set_title(f'Segment {seg_idx:02d}: Method2 Power Spectrum Decomposition',
                         fontsize=14, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            ax2.legend()

            # 左下图：幅值对齐后的幅度谱对比
            ax3.semilogy(display_freqs, test_amp_vis, 'b-', linewidth=1.0, alpha=0.8, label='Test Signal')
            ax3.semilogy(display_freqs, ref_amp_scaled_vis, 'g-', linewidth=1.0, alpha=0.8,
                        label=f'Reference Signal (Scaled ×{scaling_factor:.3f})')

            # 显示方法二的主频置零区域
            # 添加主频置零区域的阴影
            ax3.axvspan(fundamental_freq_start, fundamental_freq_end, alpha=0.3, color='yellow',
                       label=f'Method2 Excluded Band ({fundamental_bandwidth_percent:.1f}% bandwidth)')

            # 显示100Hz分界线
            ax3.axvline(100, color='red', linestyle='--', alpha=0.8, linewidth=2, label='100Hz Power Calculation Start')

            # 标记主频
            ax3.plot(test_fundamental_freq, test_amp_vis[test_peak_idx], 'o', color='blue', markersize=8,
                    markeredgecolor='darkblue', markeredgewidth=1.5, label=f'Test Peak {test_fundamental_freq:.1f}Hz')
            ref_peak_idx_scaled = np.argmin(np.abs(display_freqs - ref_fundamental_freq))
            ax3.plot(ref_fundamental_freq, ref_amp_scaled_vis[ref_peak_idx_scaled], 's', color='green', markersize=8,
                    markeredgecolor='darkgreen', markeredgewidth=1.5, label=f'Ref Peak (Aligned) {ref_fundamental_freq:.1f}Hz')

            ax3.set_xlim(min_freq_display, max_freq_display)
            ax3.set_xlabel('Frequency (Hz)')
            ax3.set_ylabel('Amplitude Spectrum')
            ax3.set_title(f'Segment {seg_idx:02d}: Amplitude Aligned Spectrum',
                         fontsize=14, fontweight='bold')
            ax3.grid(True, alpha=0.3)
            ax3.legend()

            # 右下图：主频附近放大显示（对齐后）
            zoom_range = expected_freq * 2  # 显示主频±2倍频率范围
            zoom_min = max(1, expected_freq - zoom_range)
            zoom_max = min(24000, expected_freq + zoom_range)
            zoom_mask = (display_freqs >= zoom_min) & (display_freqs <= zoom_max)

            if np.any(zoom_mask):
                ax4.semilogy(display_freqs[zoom_mask], test_amp_vis[zoom_mask], 'b-', linewidth=1.5, alpha=0.8, label='Test Signal')
                ax4.semilogy(display_freqs[zoom_mask], ref_amp_scaled_vis[zoom_mask], 'g-', linewidth=1.5, alpha=0.8,
                        label=f'Reference Signal (Scaled)')

                # 显示方法二的主频置零区域（在放大图中更清晰）
                ax4.axvspan(fundamental_freq_start, fundamental_freq_end, alpha=0.4, color='yellow',
                           label=f'Method2 Excluded Band ({fundamental_bandwidth_percent:.1f}% bandwidth)')

                # 添加置零区域的边界线
                ax4.axvline(fundamental_freq_start, color='orange', linestyle='--', alpha=0.8, linewidth=1)
                ax4.axvline(fundamental_freq_end, color='orange', linestyle='--', alpha=0.8, linewidth=1)

                # 标记主频
                ax4.plot(test_fundamental_freq, test_amp_vis[test_peak_idx], 'o', color='blue', markersize=10,
                        markeredgecolor='darkblue', markeredgewidth=2, label=f'Test Peak {test_fundamental_freq:.1f}Hz')
                ax4.plot(ref_fundamental_freq, ref_amp_scaled_vis[ref_peak_idx_scaled], 's', color='green', markersize=10,
                        markeredgecolor='darkgreen', markeredgewidth=2, label=f'Ref Peak (Aligned) {ref_fundamental_freq:.1f}Hz')

                ax4.set_xlim(zoom_min, zoom_max)

            ax4.set_xlabel('Frequency (Hz)')
            ax4.set_ylabel('Amplitude Spectrum')

            # 在标题中显示功率和THD+N结果
            title = f'Segment {seg_idx:02d}: Fundamental Frequency Zoom (Aligned)\n'
            title += f'Test Power: {test_power:.2e} | Ref Power: {ref_power:.2e}\n'
            title += f'Method1 THD+N: {thd_n_method1:.3f}% | Method2 THD+N: {thd_n_method2:.3f}%'
            ax4.set_title(title, fontsize=12, fontweight='bold')
            ax4.grid(True, alpha=0.3)
            ax4.legend()

            # 添加详细信息文本框
            info_text = f'频段 {seg_idx} 分析结果:\n'
            info_text += f'期望频率: {expected_freq:.1f} Hz\n'
            info_text += f'待测峰值: {test_fundamental_freq:.1f} Hz\n'
            info_text += f'参考峰值: {ref_fundamental_freq:.1f} Hz\n'
            info_text += f'频率偏差: {abs(test_fundamental_freq - ref_fundamental_freq):.1f} Hz\n'
            info_text += f'缩放因子: {scaling_factor:.6f}\n'
            info_text += f'Test Power: {test_power:.2e}\n'
            info_text += f'Ref Power: {ref_power:.2e}\n'
            info_text += f'Power Diff: {noise_power:.2e}\n'
            info_text += f'Method1 THD+N: {thd_n_method1:.3f}%\n'
            info_text += f'Method2 THD+N: {thd_n_method2:.3f}%'

            # 在右上角添加信息框
            ax1.text(0.98, 0.98, info_text, transform=ax1.transAxes, fontsize=9,
                    verticalalignment='top', horizontalalignment='right',
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

            plt.tight_layout()

            # 保存图像
            image_path = os.path.join(output_dir, f"segment_{seg_idx:02d}_{expected_freq:.0f}Hz_amplitude_aligned.png")
            plt.savefig(image_path, dpi=150, bbox_inches='tight')
            plt.close()

            print(f"    保存图像: {os.path.basename(image_path)}")
        
        # 打印结果
        print(f"    待测信号: 峰值频率={test_fundamental_freq:.1f}Hz, 幅值={test_peak_amp:.2e}")
        print(f"    参考信号: 峰值频率={ref_fundamental_freq:.1f}Hz, 幅值={ref_peak_amp:.2e}")
        print(f"    幅值对齐: 缩放因子={scaling_factor:.6f}")
        print(f"    方法对比: 方法1={thd_n_method1:.3f}%, 方法2={thd_n_method2:.3f}%, 差值={abs(thd_n_method1-thd_n_method2):.3f}%")

        return True, {
            'segment_idx': seg_idx,
            'expected_freq': expected_freq,
            'test_fundamental_freq': test_fundamental_freq,
            'ref_fundamental_freq': ref_fundamental_freq,
            'freq_deviation': abs(test_fundamental_freq - ref_fundamental_freq),
            'test_peak_amp': test_peak_amp,
            'ref_peak_amp': ref_peak_amp,
            'scaling_factor': scaling_factor,
            'total_test_power': test_power,
            'total_scaled_ref_power': ref_power,
            'thd_n_method1': thd_n_method1,
            'thd_n_method2': thd_n_method2,
            'thd_power_method2': thd_power_method2,
            'total_power_freq': total_power_freq,
            'fundamental_power': fundamental_power,
            'bandwidth_percent': fundamental_bandwidth_percent,
            'bandwidth_type': bandwidth_type,
            'bandwidth_value': bandwidth_value,
            'bandwidth_hz': bandwidth_hz
        }
        
    except Exception as e:
        print(f"    ❌ 频段 {seg_idx} 分析失败: {str(e)}")
        return False, None

def main(test_audio_path=None, output_dir=None):
    """
    主函数：自适应幅值缩放 + 频域相减主频去除
    """
    # 如果没有提供参数，使用默认值
    if test_audio_path is None:
        test_audio_path = "test20250722/鼓膜破裂（复测1.1).wav"
    
    print(f"🎯 THD+N双方法计算分析")
    print(f"📁 待测音频: {test_audio_path}")
    print(f"🔧 参考音频: gen_freq_step参考信号")
    print("="*70)
    
    # 创建输出目录
    if output_dir is None:
        test_name = Path(test_audio_path).stem
        output_dir = f"{test_name}_THD+N双方法分析"
    os.makedirs(output_dir, exist_ok=True)
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 使用freq_split_optimized进行频段切割
        print("🔍 使用freq_split_optimized进行频段切割...")
        from freq_split_optimized import split_freq_steps_optimized

        step_boundaries, freq_table, alignment_info = split_freq_steps_optimized(
            test_audio_path, start_freq=100, stop_freq=24000, octave=12,
            min_cycles=10, min_duration=153, fs=48000,
            search_window_start=0.1, search_window_end=1.5,
            correlation_length=1.0, plot=False, debug=False
        )

        print(f"✅ 频段切割完成，共{len(step_boundaries)}段")
        print(f"📊 对齐信息: {alignment_info}")

        # 加载待测音频
        print("🎵 加载待测音频...")
        test_audio, sr = librosa.load(test_audio_path, sr=48000)
        print(f"✅ 待测音频加载完成，采样率: {sr}Hz，时长: {len(test_audio)/sr:.2f}s")

        # 生成参考信号
        print("📊 生成参考信号...")
        sine_wave, _, freq_ssample_dict = gen_freq_step(
            start_freq=100, stop_freq=24000, octave=12,
            min_cycles=10, min_duration=153, fs=48000
        )

        print(f"✅ 参考信号生成完成，时长: {len(sine_wave)/sr:.2f}s")

        # 参考信号使用gen_freq_step的原始切割（从0开始）
        ref_step_boundaries = []
        ref_frequency_points = []

        for freq in sorted(freq_ssample_dict.keys()):
            start_time, _, duration_samples = freq_ssample_dict[freq]
            end_time = start_time + duration_samples / 48000
            ref_step_boundaries.append((start_time, end_time))
            ref_frequency_points.append(freq)

        # 使用参考信号
        ref_audio = sine_wave

        print(f"📊 待测信号: {len(step_boundaries)}段 (freq_split_optimized)")
        print(f"📊 参考信号: {len(ref_step_boundaries)}段 (gen_freq_step)")

        # 确保两个信号的频段数一致
        min_segments = min(len(step_boundaries), len(ref_step_boundaries))
        step_boundaries = step_boundaries[:min_segments]
        ref_step_boundaries = ref_step_boundaries[:min_segments]
        frequency_points = [freq_table[i] for i in range(min_segments)]

        print(f"📊 使用前{min_segments}段进行对比分析")
        
        # 准备多进程任务
        print("🚀 准备多进程分析...")
        tasks = []
        
        for i in range(min_segments):
            test_start_time, test_end_time = step_boundaries[i]
            ref_start_time, ref_end_time = ref_step_boundaries[i]
            expected_freq = frequency_points[i]
            tasks.append((
                i+1,  # seg_idx
                test_start_time,
                test_end_time,
                ref_start_time,
                ref_end_time,
                expected_freq,
                test_audio,
                ref_audio,
                sr,
                output_dir
            ))
        
        # 多进程并行处理
        print(f"📊 使用4个进程并行分析{len(tasks)}个频段...")
        
        start_time = time.time()
        results = []
        
        with ProcessPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(analyze_segment_adaptive_removal, task) for task in tasks]
            
            for future in futures:
                success, result = future.result()
                if success and result:
                    results.append(result)
        
        end_time = time.time()
        
        # 保存汇总结果
        summary_file = os.path.join(output_dir, "THD+N双方法分析汇总.txt")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("THD+N双方法计算分析汇总\n")
            f.write("="*60 + "\n\n")
            f.write(f"待测音频: {test_audio_path}\n")
            f.write(f"参考音频: gen_freq_step参考信号\n")
            f.write(f"分析时间: {end_time - start_time:.1f}秒\n")
            f.write(f"成功分析: {len(results)}/{len(tasks)}个频段\n\n")
            
            f.write(f"{'序号':<4} {'期望频率':<10} {'待测峰值':<10} {'参考峰值':<10} {'频率偏差':<10} {'缩放因子':<12} {'待测功率':<12} {'参考功率':<12} {'方法1THD+N':<12} {'方法2THD+N':<12} {'带宽策略':<10} {'带宽值':<10} {'方法差值':<12}\n")
            f.write("-" * 190 + "\n")


            total_freq_dev = 0
            total_method1 = 0
            total_method2 = 0
            total_test_power_sum = 0
            total_ref_power_sum = 0
            for result in results:
                result['freq_deviation'] = abs(result['test_fundamental_freq'] - result['ref_fundamental_freq'])
                method_diff = abs(result['thd_n_method1'] - result['thd_n_method2'])
                # 格式化带宽策略显示
                bandwidth_strategy = result['bandwidth_type']
                if result['bandwidth_type'] == 'percent':
                    bandwidth_display = f"{result['bandwidth_value']:.1f}%"
                else:
                    bandwidth_display = f"{result['bandwidth_value']:.0f}Hz"

                f.write(f"{result['segment_idx']:<4} {result['expected_freq']:<10.1f} "
                       f"{result['test_fundamental_freq']:<10.1f} {result['ref_fundamental_freq']:<10.1f} "
                       f"{result['freq_deviation']:<10.1f} {result['scaling_factor']:<12.6f} "
                       f"{result['total_test_power']:<12.3e} {result['total_scaled_ref_power']:<12.3e} "
                       f"{result['thd_n_method1']:<12.3f} {result['thd_n_method2']:<12.3f} "
                       f"{bandwidth_strategy:<10} {bandwidth_display:<10} {method_diff:<12.3f}\n")

                total_freq_dev += result['freq_deviation']
                total_method1 += result['thd_n_method1']
                total_method2 += result['thd_n_method2']
                total_test_power_sum += result['total_test_power']
                total_ref_power_sum += result['total_scaled_ref_power']

            f.write("\n统计信息:\n")

            f.write(f"  平均频率偏差: {total_freq_dev/len(results):.3f}Hz\n")
            f.write(f"  平均方法1 THD+N: {total_method1/len(results):.3f}%\n")
            f.write(f"  平均方法2 THD+N: {total_method2/len(results):.3f}%\n")
            f.write(f"  方法差值平均: {abs(total_method1-total_method2)/len(results):.3f}%\n")
            f.write(f"  待测音频总功率: {total_test_power_sum:.6f}\n")
            f.write(f"  参考音频总功率: {total_ref_power_sum:.6f}\n")
            f.write(f"  功率差: {total_test_power_sum - total_ref_power_sum:.6f}\n")
            f.write(f"  功率比: {total_test_power_sum/total_ref_power_sum:.6f}\n")

            # 计算统计信息
            scaling_factors = [r['scaling_factor'] for r in results]
            freq_deviations = [r['freq_deviation'] for r in results]
            test_powers = [r['total_test_power'] for r in results]
            ref_powers = [r['total_scaled_ref_power'] for r in results]
            method1_thds = [r['thd_n_method1'] for r in results]
            method2_thds = [r['thd_n_method2'] for r in results]
            bandwidth_hz_values = [r['bandwidth_hz'] for r in results]

            # 统计带宽策略使用情况
            percent_count = sum(1 for r in results if r['bandwidth_type'] == 'percent')
            fixed_count = sum(1 for r in results if r['bandwidth_type'] == 'fixed')

            f.write(f"  缩放因子范围: {min(scaling_factors):.6f} - {max(scaling_factors):.6f}\n")
            f.write(f"  频率偏差范围: {min(freq_deviations):.1f}Hz - {max(freq_deviations):.1f}Hz\n")
            f.write(f"  待测功率范围: {min(test_powers):.3e} - {max(test_powers):.3e}\n")
            f.write(f"  参考功率范围: {min(ref_powers):.3e} - {max(ref_powers):.3e}\n")
            f.write(f"  方法1THD+N范围: {min(method1_thds):.3f}% - {max(method1_thds):.3f}%\n")
            f.write(f"  方法2THD+N范围: {min(method2_thds):.3f}% - {max(method2_thds):.3f}%\n")
            f.write(f"  带宽策略统计: 百分比策略{percent_count}个, 固定策略{fixed_count}个\n")
            f.write(f"  实际带宽范围: {min(bandwidth_hz_values):.1f}Hz - {max(bandwidth_hz_values):.1f}Hz\n")

        
        print("\n" + "="*70)
        print("✅ THD+N双方法分析完成!")
        print(f"  成功分析: {len(results)}/{len(tasks)} 个频段")
        print(f"  总耗时: {end_time - start_time:.1f}秒")
        print(f"  输出目录: {output_dir}")
        print(f"📄 分析汇总已保存: {summary_file}")
        
        return True

    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
